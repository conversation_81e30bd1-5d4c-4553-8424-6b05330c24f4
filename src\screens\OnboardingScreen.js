import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  interpolate,
  Extrapolate
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { useProfile } from '../contexts/ProfileContext';
import { getErrorMessage, getErrorTitle, logError, isRetryableError } from '../utils/errorHandler';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import { toast } from '../utils/toast';
import spacingScale from '../constants/spacing';

const COFFEE_PREFERENCES = [
  'Mey<PERSON>msi', '<PERSON><PERSON>lat<PERSON>', '<PERSON>', 'Düşük Asidite', 
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Orta Kavrum', 'Koyu Kavrum'
];

const USAGE_PURPOSES = [
  { id: 'study', label: 'Ders Çalışmak', icon: '📚' },
  { id: 'taste', label: 'Lezzet Keşfi', icon: '☕' },
  { id: 'social', label: 'Sosyalleşmek', icon: '🤝' },
  { id: 'work', label: 'Çalışmak', icon: '💻' },
  { id: 'relax', label: 'Dinlenmek', icon: '🧘' },
  { id: 'date', label: 'Buluşmak', icon: '💕' }
];

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(0);
  const [username, setUsername] = useState('');
  const [selectedPreferences, setSelectedPreferences] = useState([]);
  const [selectedPurposeIds, setSelectedPurposeIds] = useState([]); // Changed to store IDs
  const [loading, setLoading] = useState(false);
  const [completionAttempts, setCompletionAttempts] = useState(0);
  const [showRecoveryOptions, setShowRecoveryOptions] = useState(false);

  // Use profile context for triggering refresh
  const { forceProfileRefresh, setProfileUpdating } = useProfile();

  // Animation values
  const step1Opacity = useSharedValue(0);
  const step2Opacity = useSharedValue(0);
  const step3Opacity = useSharedValue(0);
  const step1TranslateY = useSharedValue(50);
  const step2TranslateY = useSharedValue(50);
  const step3TranslateY = useSharedValue(50);

  useEffect(() => {
    step1Opacity.value = withTiming(1, { duration: 600 });
    step1TranslateY.value = withTiming(0, { duration: 600 });
  }, []);

  useEffect(() => {
    if (currentStep >= 1) {
      step2Opacity.value = withDelay(200, withTiming(1, { duration: 600 }));
      step2TranslateY.value = withDelay(200, withTiming(0, { duration: 600 }));
    }
    if (currentStep >= 2) {
      step3Opacity.value = withDelay(400, withTiming(1, { duration: 600 }));
      step3TranslateY.value = withDelay(400, withTiming(0, { duration: 600 }));
    }
  }, [currentStep]);

  const step1Style = useAnimatedStyle(() => ({
    opacity: step1Opacity.value,
    transform: [{ translateY: step1TranslateY.value }],
  }));

  const step2Style = useAnimatedStyle(() => ({
    opacity: step2Opacity.value,
    transform: [{ translateY: step2TranslateY.value }],
  }));

  const step3Style = useAnimatedStyle(() => ({
    opacity: step3Opacity.value,
    transform: [{ translateY: step3TranslateY.value }],
  }));

  const togglePreference = (preference) => {
    setSelectedPreferences(prev => 
      prev.includes(preference) 
        ? prev.filter(p => p !== preference)
        : [...prev, preference]
    );
  };

  const togglePurpose = (purposeId) => {
    setSelectedPurposeIds(prev => 
      prev.includes(purposeId) 
        ? prev.filter(p => p !== purposeId)
        : [...prev, purposeId]
    );
  };

  const handleNext = () => {
    if (currentStep === 0 && username.trim()) {
      setCurrentStep(1);
    } else if (currentStep === 1 && selectedPreferences.length > 0) {
      setCurrentStep(2);
    }
  };

  const validateOnboardingData = () => {
    if (!username.trim()) {
      toast.warning('Eksik Bilgi', 'Lütfen bir kullanıcı adı girin.');
      return false;
    }

    if (username.trim().length < 3) {
      toast.error('Geçersiz Kullanıcı Adı', 'Kullanıcı adı en az 3 karakter olmalıdır.');
      return false;
    }

    if (!/^[a-zA-Z0-9_]+$/.test(username.trim())) {
      toast.error('Geçersiz Kullanıcı Adı', 'Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir.');
      return false;
    }

    if (selectedPreferences.length === 0) {
      toast.warning('Eksik Bilgi', 'Lütfen en az bir kahve tercihi seçin.');
      return false;
    }

    if (selectedPurposeIds.length === 0) {
      toast.warning('Eksik Bilgi', 'Lütfen en az bir kullanım amacı seçin.');
      return false;
    }

    return true;
  };

  // Enhanced error handling with comprehensive error categorization
  const handleOnboardingError = (error, context = 'onboarding') => {
    logError(error, context, {
      username: username,
      step: currentStep,
      preferencesCount: selectedPreferences.length,
      purposeCount: selectedPurposeIds.length
    });

    const title = getErrorTitle(error, context);
    const message = getErrorMessage(error, context);

    toast.error(title, message);

    return { title, message, isRetryable: isRetryableError(error) };
  };

  // Profil oluşturma/güncelleme fonksiyonu (UPDATE with INSERT fallback)
  const upsertProfile = async (userId, profileData) => {
    console.log('🔄 Starting profile upsert for user:', userId);

    try {
      // Önce UPDATE dene
      const { data: updateData, error: updateError } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', userId)
        .select()
        .single();

      if (updateError) {
        logError(updateError, 'profile_update', { userId, operation: 'UPDATE' });

        // Eğer profil bulunamadıysa (PGRST116), INSERT dene
        if (updateError.code === 'PGRST116') {
          console.log('📝 Profile not found, attempting INSERT...');

          const { data: insertData, error: insertError } = await supabase
            .from('profiles')
            .insert({
              id: userId,
              ...profileData
            })
            .select()
            .single();

          if (insertError) {
            logError(insertError, 'profile_insert', { userId, operation: 'INSERT' });
            throw insertError;
          }

          console.log('✅ Profile created successfully via INSERT');
          return { data: insertData, error: null };
        } else {
          // Diğer UPDATE hataları için throw
          throw updateError;
        }
      }

      console.log('✅ Profile updated successfully via UPDATE');
      return { data: updateData, error: null };

    } catch (error) {
      console.error('❌ Profile upsert failed:', error);
      return { data: null, error };
    }
  };

  const handleComplete = async () => {
    if (!validateOnboardingData()) {
      return;
    }

    // Track completion attempts to prevent infinite loops
    const currentAttempts = completionAttempts + 1;
    setCompletionAttempts(currentAttempts);

    // Show recovery options after multiple failed attempts
    if (currentAttempts > 3) {
      setShowRecoveryOptions(true);
      toast.warning('Çoklu Deneme', 'Profil tamamlama işlemi birkaç kez başarısız oldu. Kurtarma seçenekleri gösteriliyor.');
    }

    setLoading(true);
    try {
      console.log(`🚀 Starting onboarding completion process... (attempt ${currentAttempts})`);

      // Kullanıcı oturumunu doğrula
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        handleOnboardingError(userError, 'auth');
        return;
      }

      if (!user) {
        const noUserError = new Error('Kullanıcı oturumu bulunamadı');
        noUserError.code = 'no_user_session';
        handleOnboardingError(noUserError, 'auth');
        return;
      }

      console.log('👤 User validated:', user.email);

      // Profil verilerini hazırla
      const profileData = {
        username: username.trim().toLowerCase(),
        coffee_preferences: selectedPreferences,
        usage_purpose: selectedPurposeIds,
        updated_at: new Date().toISOString(),
      };

      console.log('📝 Profile data prepared:', {
        username: profileData.username,
        preferencesCount: profileData.coffee_preferences.length,
        purposeCount: profileData.usage_purpose.length
      });

      // Profil oluştur/güncelle (UPDATE with INSERT fallback)
      const { data: profileResult, error: profileError } = await upsertProfile(user.id, profileData);

      if (profileError) {
        handleOnboardingError(profileError, 'onboarding');
        return;
      }

      console.log('✅ Profile upsert successful:', profileResult);
      toast.success('Profil Tamamlandı!', 'Hoş geldiniz! Artık uygulamayı kullanabilirsiniz.');

      // Kısa bir bekleme süresi (database commit için)
      await new Promise(resolve => setTimeout(resolve, 300));

      // Profil güncellemesini doğrula
      console.log('🔍 Verifying profile update...');
      const { data: verifyData, error: verifyError } = await supabase
        .from('profiles')
        .select('username, coffee_preferences, usage_purpose')
        .eq('id', user.id)
        .single();

      if (verifyError) {
        console.warn('⚠️ Profile verification failed:', verifyError);
        toast.warning('Doğrulama Uyarısı', 'Profil kaydedildi ancak doğrulanamadı. Uygulama yeniden başlatılabilir.');

        // Even if verification fails, trigger profile refresh
        console.log('🔄 Triggering profile refresh despite verification failure...');
        forceProfileRefresh();

      } else if (verifyData?.username === profileData.username) {
        console.log('✅ Profile update verified successfully');

        // Trigger profile refresh in AppNavigator using context
        console.log('🔄 Triggering profile refresh in AppNavigator via context...');
        forceProfileRefresh();

      } else {
        console.warn('⚠️ Profile verification mismatch');
        toast.warning('Doğrulama Uyarısı', 'Profil kaydedildi ancak doğrulamada uyumsuzluk var.');

        // Still trigger refresh to attempt navigation
        console.log('🔄 Triggering profile refresh despite mismatch...');
        forceProfileRefresh();
      }

    } catch (error) {
      console.error('❌ Unexpected onboarding error:', error);

      // Hata türüne göre daha spesifik mesajlar
      if (error.message?.includes('network') || error.message?.includes('fetch')) {
        toast.error('Bağlantı Hatası', 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.');
      } else if (error.message?.includes('timeout')) {
        toast.error('Zaman Aşımı', 'İşlem çok uzun sürdü. Lütfen tekrar deneyin.');
      } else {
        toast.error('Beklenmeyen Hata', 'Bir sorun oluştu. Lütfen tekrar deneyin.');
      }
    } finally {
      setLoading(false);
      console.log('🏁 Onboarding completion process finished');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={[styles.title, globalStyles.heading]}>Profilinizi Oluşturalım</Text>
          <Text style={[styles.subtitle, globalStyles.body]}>
            Size özel kahve deneyimi için birkaç bilgiye ihtiyacımız var
          </Text>
        </View>

      {/* Step 1: Username */}
      <Animated.View style={[styles.section, step1Style]}>
        <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kullanıcı Adınız</Text>
        <TextInput
          style={[styles.input, globalStyles.body]}
          placeholder="Benzersiz bir kullanıcı adı seçin"
          placeholderTextColor={Colors.tabIconDefault}
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
        />
        {username.trim() && currentStep === 0 && (
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={[styles.nextButtonText, globalStyles.body]}>Devam Et</Text>
          </TouchableOpacity>
        )}
      </Animated.View>

      {/* Step 2: Coffee Preferences */}
      {currentStep >= 1 && (
        <Animated.View style={[styles.section, step2Style]}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Favori Tatlarınız</Text>
          <Text style={[styles.sectionSubtitle, globalStyles.bodySmall]}>
            Hangi kahve tatlarını seviyorsunuz? (Birden fazla seçebilirsiniz)
          </Text>
          <View style={styles.tagsContainer}>
            {COFFEE_PREFERENCES.map((preference) => (
              <TouchableOpacity
                key={preference}
                style={[
                  styles.tag,
                  selectedPreferences.includes(preference) && styles.tagSelected
                ]}
                onPress={() => togglePreference(preference)}
              >
                <Text style={[
                  styles.tagText,
                  globalStyles.bodySmall,
                  selectedPreferences.includes(preference) && styles.tagTextSelected
                ]}>
                  {preference}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {selectedPreferences.length > 0 && currentStep === 1 && (
            <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
              <Text style={[styles.nextButtonText, globalStyles.body]}>Devam Et</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      )}

      {/* Step 3: Usage Purpose */}
      {currentStep >= 2 && (
        <Animated.View style={[styles.section, step3Style]}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kullanım Amacınız</Text>
          <Text style={[styles.sectionSubtitle, globalStyles.bodySmall]}>
            Uygulamayı ne için kullanacaksınız?
          </Text>
          <View style={styles.cardsContainer}>
            {USAGE_PURPOSES.map((purpose) => (
              <TouchableOpacity
                key={purpose.id}
                style={[
                  styles.purposeCard,
                  selectedPurposeIds.includes(purpose.id) && styles.purposeCardSelected
                ]}
                onPress={() => togglePurpose(purpose.id)}
              >
                <Text style={styles.purposeIcon}>{purpose.icon}</Text>
                <Text style={[
                  styles.purposeText,
                  globalStyles.bodySmall,
                  selectedPurposeIds.includes(purpose.id) && styles.purposeTextSelected
                ]}>
                  {purpose.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          {selectedPurposeIds.length > 0 && (
            <TouchableOpacity 
              style={[styles.completeButton, loading && styles.buttonDisabled]}
              onPress={handleComplete}
              disabled={loading}
            >
              {loading ? (
                <>
                  <ActivityIndicator color={Colors.text} size="small" />
                  <Text style={[styles.completeButtonText, globalStyles.body, { marginLeft: 8 }]}>
                    Profil Oluşturuluyor...
                  </Text>
                </>
              ) : (
                <Text style={[styles.completeButtonText, globalStyles.body]}>
                  Profili Tamamla
                </Text>
              )}
            </TouchableOpacity>
          )}
        </Animated.View>
      )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: spacingScale.xl,
    paddingVertical: spacingScale.xl,
    paddingBottom: spacingScale.xl + 80, // Extra padding for safe area
  },
  header: {
    marginBottom: 40,
    alignItems: 'center',
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    marginBottom: 40,
  },
  sectionTitle: {
    color: Colors.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    color: Colors.tabIconDefault,
    marginBottom: 20,
    lineHeight: 20,
  },
  input: {
    backgroundColor: Colors.card,
    color: Colors.text,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  tag: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  tagSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  tagText: {
    color: Colors.text,
  },
  tagTextSelected: {
    color: Colors.background,
  },
  cardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  purposeCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: '47%',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  purposeCardSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  purposeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  purposeText: {
    color: Colors.text,
    textAlign: 'center',
  },
  purposeTextSelected: {
    color: Colors.background,
  },
  nextButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  nextButtonText: {
    color: Colors.text,
    fontWeight: '600',
  },
  completeButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginTop: 20,
  },
  completeButtonText: {
    color: Colors.text,
    fontWeight: '600',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
});