import React, { useState, useEffect } from 'react';
import { ActivityIndicator, View, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { supabase } from '../lib/supabase';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import TabNavigator from './TabNavigator';
import VenueDetailScreen from '../screens/VenueDetailScreen';
import { Colors } from '../constants/Colors';

const Stack = createNativeStackNavigator();

export default function AppNavigator() {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const [initError, setInitError] = useState(null);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('🔍 Getting initial session...');
        const { data: { session } } = await supabase.auth.getSession();

        console.log('📱 Initial session:', session?.user?.email || 'No user');
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log('👤 User found, fetching profile...');
          await fetchProfile(session.user.id);
        } else {
          console.log('❌ No user found');
          setProfile(null);
        }
      } catch (error) {
        console.error('❌ Error getting initial session:', error);
        setInitError(error);
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
        console.log('✅ Initial session check complete');
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state changed:', event, session?.user?.email || 'No user');

      setUser(session?.user ?? null);

      if (session?.user) {
        console.log('👤 User authenticated, fetching profile...');
        setProfileLoading(true);
        try {
          await fetchProfile(session.user.id);
        } catch (error) {
          console.error('❌ Error in auth state change profile fetch:', error);
        } finally {
          setProfileLoading(false);
        }
      } else {
        console.log('❌ User signed out, clearing profile');
        setProfile(null);
        setProfileLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId) => {
    try {
      console.log('🔍 Fetching profile for user:', userId);

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Profile fetch error:', error.code, error.message);

        if (error.code === 'PGRST116') {
          console.log('📝 Profile not found, user needs onboarding');
          setProfile(null);
        } else {
          console.log('❌ Other profile error, setting profile to null');
          setProfile(null);
        }
        return;
      }

      if (data) {
        console.log('✅ Profile found:', {
          username: data.username || 'No username',
          hasUsername: !!data.username,
          profileData: data
        });
        setProfile(data);
      } else {
        console.log('❌ No profile data returned');
        setProfile(null);
      }
    } catch (error) {
      console.error('❌ Unexpected error fetching profile:', error);
      setProfile(null);
    }
  };

  // Show error screen if initialization failed
  if (initError) {
    console.log('❌ Showing error screen:', initError.message);
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background, padding: 20 }}>
        <Text style={{ color: Colors.text, fontSize: 18, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
          Bağlantı Hatası
        </Text>
        <Text style={{ color: Colors.secondary, fontSize: 14, textAlign: 'center', marginBottom: 20 }}>
          Uygulama başlatılırken bir hata oluştu. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.
        </Text>
        <Text style={{ color: Colors.secondary, fontSize: 12, textAlign: 'center', fontFamily: 'monospace' }}>
          Hata: {initError.message}
        </Text>
      </View>
    );
  }

  // Show loading screen during initial auth check
  if (loading) {
    console.log('⏳ Showing loading screen...');
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background }}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Show profile loading if user exists but profile is being fetched
  if (user && profileLoading) {
    console.log('⏳ User exists, loading profile...');
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background }}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Debug current state
  console.log('🎯 Navigation decision:', {
    user: user?.email || 'No user',
    profile: profile?.username || 'No profile',
    hasProfile: !!profile,
    hasUsername: !!profile?.username,
    profileLoading: profileLoading,
    loading: loading
  });

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
          animationDuration: 250,
          gestureEnabled: true,
          gestureDirection: 'horizontal',
        }}
      >
        {!user ? (
          // Auth Stack - User is not logged in
          <>
            {console.log('🔐 Showing Auth Stack (Login/Register)')}
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="Register" component={RegisterScreen} />
          </>
        ) : user && profile && profile.username ? (
          // Main App Stack - User is logged in and profile is complete
          <>
            {console.log('🏠 Showing Main App Stack')}
            <Stack.Screen name="MainApp" component={TabNavigator} />
            <Stack.Screen
              name="VenueDetail"
              component={VenueDetailScreen}
              options={{
                headerShown: true,
                headerTitle: 'Mekan Detayı',
                headerStyle: { backgroundColor: Colors.card },
                headerTintColor: Colors.text
              }}
            />
          </>
        ) : user && (!profile || !profile.username) ? (
          // Onboarding Stack - User is logged in but profile is incomplete or missing
          <>
            {console.log('📝 Showing Onboarding Stack')}
            <Stack.Screen name="Onboarding" component={OnboardingScreen} />
          </>
        ) : (
          // Fallback - should not happen, but show auth if it does
          <>
            {console.log('⚠️ Fallback to Auth Stack')}
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="Register" component={RegisterScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}