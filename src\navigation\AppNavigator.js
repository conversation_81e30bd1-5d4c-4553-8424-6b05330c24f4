 import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { ActivityIndicator, View, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { supabase } from '../lib/supabase';
import { useProfile } from '../contexts/ProfileContext';
import { getErrorMessage, logError, isRetryableError } from '../utils/errorHandler';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import TabNavigator from './TabNavigator';
import VenueDetailScreen from '../screens/VenueDetailScreen';
import { Colors } from '../constants/Colors';

const Stack = createNativeStackNavigator();

export default function AppNavigator() {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const [initError, setInitError] = useState(null);
  const [authStateStable, setAuthStateStable] = useState(false);
  const [profileFetchAttempts, setProfileFetchAttempts] = useState(0);
  const [lastProfileFetch, setLastProfileFetch] = useState(null);
  const [navigationLoopCount, setNavigationLoopCount] = useState(0);
  const [lastNavigationDecision, setLastNavigationDecision] = useState(null);
  const [forceAuthScreen, setForceAuthScreen] = useState(false);

  // Use profile context for cross-component communication
  const { profileRefreshTrigger, profileUpdateInProgress } = useProfile();

  // Function to detect and prevent navigation loops
  const checkNavigationLoop = useCallback((currentDecision) => {
    // Don't check for loops during initial loading
    if (loading || !authStateStable) {
      return false;
    }

    const maxLoopCount = 3;

    // If same decision as last time, increment loop count
    if (lastNavigationDecision === currentDecision) {
      const newLoopCount = navigationLoopCount + 1;

      if (newLoopCount >= maxLoopCount) {
        console.error('🔄 Navigation loop detected! Forcing auth screen...');
        logError(
          new Error(`Navigation loop detected: ${currentDecision}`),
          'navigation_loop',
          {
            loopCount: newLoopCount,
            decision: currentDecision,
            user: user?.email,
            profile: profile?.username
          }
        );

        // Force user back to auth screen to break the loop
        setForceAuthScreen(true);
        setUser(null);
        setProfile(null);
        setNavigationLoopCount(0);
        setLastNavigationDecision(null);

        return true; // Loop detected and handled
      } else {
        setNavigationLoopCount(newLoopCount);
      }
    } else {
      // Different decision, reset loop count
      setNavigationLoopCount(0);
      setLastNavigationDecision(currentDecision);
    }

    return false; // No loop detected
  }, [loading, authStateStable, lastNavigationDecision, navigationLoopCount, user?.email, profile?.username]);

  // Determine navigation decision with memoization to prevent unnecessary re-renders
  const navigationDecision = useMemo(() => {
    let decision = 'unknown';

    if (!user || forceAuthScreen) {
      decision = 'auth';
    } else if (user && profile && profile.username) {
      decision = 'main_app';
    } else if (user && (!profile || !profile.username)) {
      decision = 'onboarding';
    }

    return decision;
  }, [user, profile, forceAuthScreen]);

  // Check for navigation loops only when decision changes and app is stable
  useEffect(() => {
    if (authStateStable && !loading && !profileLoading && navigationDecision !== 'unknown') {
      const loopDetected = checkNavigationLoop(navigationDecision);
      if (loopDetected) {
        // Loop handling is done inside checkNavigationLoop
        return;
      }
    }
  }, [navigationDecision, authStateStable, loading, profileLoading, checkNavigationLoop]);

  // Debug current state (only log when decision changes)
  useEffect(() => {
    console.log('🎯 Navigation decision:', {
      user: user?.email || 'No user',
      profile: profile?.username || 'No profile',
      hasProfile: !!profile,
      hasUsername: !!profile?.username,
      profileLoading: profileLoading,
      loading: loading,
      profileFetchAttempts: profileFetchAttempts,
      authStateStable: authStateStable,
      navigationDecision: navigationDecision,
      navigationLoopCount: navigationLoopCount,
      forceAuthScreen: forceAuthScreen
    });
  }, [navigationDecision, user?.email, profile?.username, loading, profileLoading]);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('🔍 Getting initial session...');
        const { data: { session } } = await supabase.auth.getSession();

        console.log('📱 Initial session:', session?.user?.email || 'No user');
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log('👤 User found, fetching profile...');
          await fetchProfile(session.user.id);
        } else {
          console.log('❌ No user found');
          setProfile(null);
        }
      } catch (error) {
        console.error('❌ Error getting initial session:', error);
        setInitError(error);
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
        console.log('✅ Initial session check complete');
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state changed:', event, session?.user?.email || 'No user');

      setUser(session?.user ?? null);

      // Mark auth state as stable after a short delay to prevent immediate loop detection
      setTimeout(() => {
        setAuthStateStable(true);
      }, 100);

      if (session?.user) {
        console.log('👤 User authenticated, fetching profile...');
        setProfileLoading(true);
        setProfileFetchAttempts(0); // Reset attempts for new user

        try {
          await fetchProfile(session.user.id);
        } catch (error) {
          console.error('❌ Error in auth state change profile fetch:', error);
          setInitError(new Error('Profil yüklenirken hata oluştu.'));
        } finally {
          setProfileLoading(false);
        }
      } else {
        console.log('❌ User signed out, clearing profile');
        setProfile(null);
        setProfileLoading(false);
        setProfileFetchAttempts(0);
        setInitError(null);
        setLastProfileFetch(null);
        // Reset loop detection state on sign out
        setNavigationLoopCount(0);
        setLastNavigationDecision(null);
        setForceAuthScreen(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Listen for profile refresh triggers from context
  useEffect(() => {
    if (profileRefreshTrigger > 0 && user) {
      console.log('🔄 Profile refresh triggered from context, refetching profile...');
      setProfileLoading(true);
      fetchProfile(user.id)
        .then(() => {
          console.log('✅ Profile refresh completed');
        })
        .catch((error) => {
          console.error('❌ Profile refresh failed:', error);
        })
        .finally(() => {
          setProfileLoading(false);
        });
    }
  }, [profileRefreshTrigger, user]);

  const fetchProfile = async (userId, retryCount = 0) => {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second

    try {
      console.log(`🔍 Fetching profile for user: ${userId} (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Prevent too frequent fetches
      const now = Date.now();
      if (lastProfileFetch && (now - lastProfileFetch) < 500) {
        console.log('⏳ Profile fetch too frequent, skipping...');
        return;
      }
      setLastProfileFetch(now);

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Profile fetch error:', error.code, error.message);

        // Handle different error types
        if (error.code === 'PGRST116') {
          console.log('📝 Profile not found, user needs onboarding');
          setProfile(null);
          setProfileFetchAttempts(0); // Reset attempts on successful determination
          return;
        } else {
          // Use comprehensive error handling
          logError(error, 'profile_fetch', { userId, retryCount });

          const errorMessage = getErrorMessage(error, 'profile');

          if (isRetryableError(error) && retryCount < maxRetries) {
            console.log(`🔄 Retrying profile fetch in ${retryDelay}ms...`);
            setTimeout(() => {
              setProfileFetchAttempts(retryCount + 1);
              fetchProfile(userId, retryCount + 1);
            }, retryDelay);
            return;
          } else {
            console.error('❌ Profile fetch failed after retries');
            setProfile(null);
            setInitError(new Error(errorMessage));
          }
        }
        return;
      }

      if (data) {
        console.log('✅ Profile found:', {
          username: data.username || 'No username',
          hasUsername: !!data.username,
          email: data.email,
          createdAt: data.created_at
        });
        setProfile(data);
        setProfileFetchAttempts(0); // Reset attempts on success
        setInitError(null); // Clear any previous errors
      } else {
        console.log('❌ No profile data returned');
        setProfile(null);
      }

    } catch (error) {
      console.error('❌ Unexpected error fetching profile:', error);

      // Retry on unexpected errors
      if (retryCount < maxRetries) {
        console.log(`🔄 Retrying profile fetch after unexpected error in ${retryDelay}ms...`);
        setTimeout(() => {
          setProfileFetchAttempts(retryCount + 1);
          fetchProfile(userId, retryCount + 1);
        }, retryDelay);
      } else {
        console.error('❌ Max retries reached after unexpected error');
        setProfile(null);
        setInitError(new Error('Profil yüklenirken beklenmeyen bir hata oluştu.'));
      }
    }
  };

  // Show error screen if initialization failed
  if (initError) {
    console.log('❌ Showing error screen:', initError.message);
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background, padding: 20 }}>
        <Text style={{ color: Colors.text, fontSize: 18, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
          Bağlantı Hatası
        </Text>
        <Text style={{ color: Colors.secondary, fontSize: 14, textAlign: 'center', marginBottom: 20 }}>
          Uygulama başlatılırken bir hata oluştu. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.
        </Text>
        <Text style={{ color: Colors.secondary, fontSize: 12, textAlign: 'center', fontFamily: 'monospace' }}>
          Hata: {initError.message}
        </Text>
      </View>
    );
  }

  // Show loading screen during initial auth check
  if (loading) {
    console.log('⏳ Showing loading screen...');
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background }}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Show profile loading if user exists but profile is being fetched
  if (user && profileLoading) {
    console.log('⏳ User exists, loading profile...');
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background }}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={{ color: Colors.secondary, marginTop: 16, fontSize: 14 }}>
          Profil yükleniyor...
        </Text>
      </View>
    );
  }

  // Show loading if user exists but we haven't determined profile state yet
  if (user && profile === null && !profileLoading && profileFetchAttempts === 0) {
    console.log('⏳ User exists but profile state undetermined, starting profile fetch...');
    // This will trigger the profile fetch in the auth state change listener
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background }}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={{ color: Colors.secondary, marginTop: 16, fontSize: 14 }}>
          Hesap bilgileri kontrol ediliyor...
        </Text>
      </View>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
          animationDuration: 250,
          gestureEnabled: true,
          gestureDirection: 'horizontal',
        }}
      >
        {navigationDecision === 'auth' ? (
          // Auth Stack - User is not logged in or forced to auth
          <>
            {console.log('🔐 Showing Auth Stack (Login/Register)')}
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="Register" component={RegisterScreen} />
          </>
        ) : navigationDecision === 'main_app' ? (
          // Main App Stack - User is logged in and profile is complete
          <>
            {console.log('🏠 Showing Main App Stack')}
            <Stack.Screen name="MainApp" component={TabNavigator} />
            <Stack.Screen
              name="VenueDetail"
              component={VenueDetailScreen}
              options={{
                headerShown: true,
                headerTitle: 'Mekan Detayı',
                headerStyle: { backgroundColor: Colors.card },
                headerTintColor: Colors.text
              }}
            />
          </>
        ) : navigationDecision === 'onboarding' ? (
          // Onboarding Stack - User is logged in but profile is incomplete or missing
          <>
            {console.log('📝 Showing Onboarding Stack')}
            <Stack.Screen name="Onboarding" component={OnboardingScreen} />
          </>
        ) : (
          // Fallback - should not happen, but show auth if it does
          <>
            {console.log('⚠️ Fallback to Auth Stack')}
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="Register" component={RegisterScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}