# SipTracker Onboarding Flow Test Guide

## Overview
This guide provides comprehensive testing instructions for the improved onboarding flow: **Registration → Login → Profile Creation (Onboarding) → Main Application Screen**.

## Pre-Testing Setup

### 1. Database Setup
First, run the database fixes in Supabase SQL Editor:

```sql
-- Execute this file in Supabase SQL Editor
-- File: database_onboarding_fixes.sql
```

### 2. Verify Environment
- Ensure `.env` file has correct Supabase credentials
- Check that all dependencies are installed: `npm install`
- Verify React Native environment is set up properly

## Testing Scenarios

### Scenario 1: New User Registration Flow
**Expected Flow**: Registration → Login → Onboarding → Main App

1. **Start the app**: `npm start` or `expo start`
2. **Navigate to Register screen**
3. **Fill registration form**:
   - Full Name: "Test User"
   - Email: "<EMAIL>" (use a unique email)
   - Password: "password123"
4. **Submit registration**
5. **Expected Result**: 
   - Success toast: "<PERSON><PERSON><PERSON> Başarılı!"
   - Auto-redirect to Login screen after 2 seconds
   - Database should have created a profile with `full_name` but no `username`

### Scenario 2: Login After Registration
**Expected Flow**: Login → Onboarding (since profile incomplete)

1. **On Login screen, enter credentials**:
   - Email: "<EMAIL>"
   - Password: "password123"
2. **Submit login**
3. **Expected Result**:
   - Success toast: "Hoş Geldiniz!"
   - App should detect incomplete profile (no username)
   - Navigate to Onboarding screen

### Scenario 3: Complete Onboarding Process
**Expected Flow**: Onboarding Steps → Profile Creation → Main App

1. **Step 1 - Username**:
   - Enter username: "testuser123"
   - Click "İleri"
2. **Step 2 - Coffee Preferences**:
   - Select at least one preference (e.g., "Meyvemsi")
   - Click "İleri"
3. **Step 3 - Usage Purpose**:
   - Select at least one purpose (e.g., "Lezzet Keşfi")
   - Click "Profili Tamamla"
4. **Expected Result**:
   - Success toast: "Profil Tamamlandı!"
   - Profile should be updated in database
   - Navigate to Main App (TabNavigator)

### Scenario 4: Returning User Login
**Expected Flow**: Login → Main App (profile complete)

1. **Restart the app** (to test session persistence)
2. **Login with same credentials**
3. **Expected Result**:
   - Should skip onboarding
   - Navigate directly to Main App

## Error Scenarios to Test

### Error Scenario 1: Network Issues
1. **Disable internet connection**
2. **Try to complete onboarding**
3. **Expected Result**:
   - Turkish error message about network
   - Retry mechanism should work when connection restored

### Error Scenario 2: Duplicate Username
1. **Complete onboarding with username "testuser123"**
2. **Create another account and try same username**
3. **Expected Result**:
   - Error: "Bu kullanıcı adı zaten alınmış"
   - User should be able to try different username

### Error Scenario 3: Invalid Data
1. **Try username with less than 3 characters**
2. **Try username with special characters**
3. **Expected Result**:
   - Validation errors in Turkish
   - Form should not submit

## Loop Prevention Testing

### Test Navigation Loop Prevention
1. **Manually cause profile fetch failures** (temporarily break database connection)
2. **Observe navigation behavior**
3. **Expected Result**:
   - After 5 failed attempts, should force user to auth screen
   - Should not get stuck in infinite loops

## Performance Testing

### Test Loading States
1. **Observe loading indicators during**:
   - Initial app load
   - Profile fetching
   - Onboarding completion
2. **Expected Result**:
   - Smooth transitions
   - No flickering screens
   - Appropriate loading messages in Turkish

## Database Verification

### Check Profile Creation
```sql
-- Run in Supabase SQL Editor to verify profiles
SELECT 
  p.id,
  p.full_name,
  p.username,
  p.email,
  p.coffee_preferences,
  p.usage_purpose,
  p.created_at,
  p.updated_at,
  u.email as auth_email
FROM profiles p
JOIN auth.users u ON p.id = u.id
ORDER BY p.created_at DESC;
```

### Check Trigger Functionality
```sql
-- Verify the trigger is working
SELECT 
  COUNT(*) as user_count,
  (SELECT COUNT(*) FROM profiles) as profile_count
FROM auth.users;
-- These numbers should match
```

## Common Issues and Solutions

### Issue 1: Profile Not Created After Registration
**Symptoms**: User logs in but gets stuck in loading
**Solution**: 
1. Check if database trigger is installed
2. Run `database_onboarding_fixes.sql`
3. Verify RLS policies are correct

### Issue 2: Onboarding Completion Fails
**Symptoms**: "Profil Güncellenemedi" error
**Solution**:
1. Check network connection
2. Verify user has proper permissions
3. Check database constraints

### Issue 3: Navigation Loops
**Symptoms**: App keeps switching between screens
**Solution**:
1. Check console logs for loop detection
2. Verify profile data integrity
3. Clear app data and restart

## Success Criteria

✅ **Registration Flow**:
- User can register successfully
- Profile is created in database
- Redirected to login screen

✅ **Login Flow**:
- User can login with registered credentials
- App detects incomplete profile
- Navigates to onboarding

✅ **Onboarding Flow**:
- All three steps work smoothly
- Profile is updated with username and preferences
- Navigates to main app

✅ **Main App Access**:
- User reaches TabNavigator
- Profile data is accessible
- No navigation loops

✅ **Error Handling**:
- Network errors show Turkish messages
- Validation errors are clear
- Recovery options work

✅ **Performance**:
- No screen flickering
- Smooth transitions
- Appropriate loading states

## Debugging Tips

### Console Logs to Watch
- `🔍 Getting initial session...`
- `👤 User authenticated, fetching profile...`
- `📝 Profile not found, user needs onboarding`
- `✅ Profile found`
- `🔄 Profile refresh triggered`
- `🎯 Navigation decision`

### Key State Variables
- `user`: Should have email when logged in
- `profile`: Should be null before onboarding, populated after
- `profile.username`: Key field that determines navigation
- `loading` and `profileLoading`: Should be false when stable

### Database Checks
1. Verify profile exists: `SELECT * FROM profiles WHERE email = '<EMAIL>'`
2. Check username: Profile should have username after onboarding
3. Verify preferences: Should be JSON arrays, not empty

## Final Verification

After completing all tests, the user flow should work seamlessly:
1. **New users**: Registration → Login → Onboarding → Main App
2. **Returning users**: Login → Main App (skip onboarding)
3. **Error recovery**: Graceful handling with Turkish messages
4. **Performance**: Smooth, no loops, proper loading states

If any step fails, refer to the troubleshooting section or check the console logs for specific error messages.
