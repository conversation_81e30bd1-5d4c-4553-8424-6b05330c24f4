import React, { useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useFonts, Poppins_400Regular, Poppins_600SemiBold } from '@expo-google-fonts/poppins';
import Toast from 'react-native-toast-message';
import AppNavigator from './src/navigation/AppNavigator';
import { ProfileProvider } from './src/contexts/ProfileContext';
import { Colors } from './src/constants/Colors';

export default function App() {
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    // Log environment info for debugging
    console.log('🚀 App starting...');
    console.log('📱 Platform:', require('react-native').Platform.OS);
    console.log('🔧 Environment check complete');
  }, []);

  if (!fontsLoaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background }}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <SafeAreaProvider>
      <ProfileProvider>
        <AppNavigator />
        <Toast />
      </ProfileProvider>
    </SafeAreaProvider>
  );
}
