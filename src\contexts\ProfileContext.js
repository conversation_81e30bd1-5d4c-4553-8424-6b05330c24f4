import React, { createContext, useContext, useState, useCallback } from 'react';

// Profile Context for managing profile state across the app
const ProfileContext = createContext();

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

export const ProfileProvider = ({ children }) => {
  const [profileRefreshTrigger, setProfileRefreshTrigger] = useState(0);
  const [profileUpdateInProgress, setProfileUpdateInProgress] = useState(false);

  // Function to trigger profile refresh in AppNavigator
  const triggerProfileRefresh = useCallback(() => {
    console.log('🔄 Profile refresh triggered from context');
    setProfileRefreshTrigger(prev => prev + 1);
  }, []);

  // Function to indicate profile update is in progress
  const setProfileUpdating = useCallback((updating) => {
    console.log('📝 Profile update status:', updating ? 'started' : 'finished');
    setProfileUpdateInProgress(updating);
  }, []);

  // Function to force immediate profile refresh (for onboarding completion)
  const forceProfileRefresh = useCallback(() => {
    console.log('🚀 Force profile refresh triggered');
    setProfileUpdateInProgress(false); // Clear any pending update status
    setProfileRefreshTrigger(prev => prev + 1);
  }, []);

  const value = {
    profileRefreshTrigger,
    profileUpdateInProgress,
    triggerProfileRefresh,
    setProfileUpdating,
    forceProfileRefresh,
  };

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};

export default ProfileContext;
