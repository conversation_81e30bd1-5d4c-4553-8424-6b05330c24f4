-- SipTracker Database Güvenlik ve Düzeltme Komutları
-- <PERSON>u komutları Supabase SQL Editor'de çalıştırın

-- 1. PROFILES TABLOSU İÇİN RLS AKTIF ET
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 2. VENUES TABLOSU İÇİN RLS AKTIF ET
ALTER TABLE public.venues ENABLE ROW LEVEL SECURITY;

-- 3. REVIEWS TABLOSU İÇİN RLS AKTIF ET
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- 4. PROFILES TABLOSU RLS POLİTİKALARI
-- <PERSON>llanı<PERSON><PERSON>lar sadece kendi profillerini görebilir ve düzenleyebilir
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 5. VENUES TABLOSU RLS POLİTİKALARI
-- Tüm kullanıcılar mekanları görebilir
CREATE POLICY "Anyone can view venues" ON public.venues
    FOR SELECT USING (true);

-- 6. REVIEWS TABLOSU RLS POLİTİKALARI
-- Tüm kullanıcılar yorumları görebilir
CREATE POLICY "Anyone can view reviews" ON public.reviews
    FOR SELECT USING (true);

-- Kullanıcılar sadece kendi yorumlarını ekleyebilir
CREATE POLICY "Users can insert own reviews" ON public.reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Kullanıcılar sadece kendi yorumlarını güncelleyebilir
CREATE POLICY "Users can update own reviews" ON public.reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Kullanıcılar sadece kendi yorumlarını silebilir
CREATE POLICY "Users can delete own reviews" ON public.reviews
    FOR DELETE USING (auth.uid() = user_id);

-- 7. EKSIK FOREIGN KEY CONSTRAINT EKLE
ALTER TABLE public.reviews 
ADD CONSTRAINT reviews_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 8. GELIŞMIŞ OTOMATIK PROFIL OLUŞTURMA TRIGGER'I
-- Kullanıcı kaydolduğunda otomatik profil oluştur
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Profil zaten varsa işlem yapma (duplicate key hatası önleme)
  IF EXISTS (SELECT 1 FROM public.profiles WHERE id = new.id) THEN
    RETURN new;
  END IF;

  -- Yeni profil oluştur
  INSERT INTO public.profiles (
    id,
    full_name,
    email,
    username,
    coffee_preferences,
    usage_purpose,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    COALESCE(new.raw_user_meta_data->>'full_name', ''),
    new.email,
    NULL, -- Username onboarding sırasında set edilecek
    '{}', -- Boş array, onboarding sırasında doldurulacak
    '{}', -- Boş array, onboarding sırasında doldurulacak
    now(),
    now()
  );

  -- Log oluştur
  RAISE LOG 'Profile created for user: % with email: %', new.id, new.email;

  RETURN new;
EXCEPTION
  WHEN unique_violation THEN
    -- Profil zaten varsa sessizce devam et
    RAISE LOG 'Profile already exists for user: %', new.id;
    RETURN new;
  WHEN OTHERS THEN
    -- Diğer hatalar için log tut ama işlemi durdurma
    RAISE LOG 'Error creating profile for user %: %', new.id, SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Eski trigger'ı sil ve yenisini oluştur
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 9. TEST VERİLERİ EKLE (Reviews tablosu için)
INSERT INTO public.reviews (venue_id, user_id, rating, content, author_name) VALUES
(1, (SELECT id FROM auth.users LIMIT 1), 5.0, 'Harika bir kahve deneyimi! Özellikle V60 demlemelerini tavsiye ederim.', 'Test Kullanıcı'),
(2, (SELECT id FROM auth.users LIMIT 1), 4.5, 'Çalışmak için mükemmel bir ortam. WiFi hızı da çok iyi.', 'Test Kullanıcı'),
(3, (SELECT id FROM auth.users LIMIT 1), 4.0, 'Kahveleri lezzetli ama biraz pahalı. Yine de tavsiye ederim.', 'Test Kullanıcı');

-- 10. PROFILES TABLOSU İÇİN EK CONSTRAINT'LER VE İNDEXLER
-- Username için unique constraint (null değerler hariç)
CREATE UNIQUE INDEX IF NOT EXISTS profiles_username_unique
ON public.profiles (username)
WHERE username IS NOT NULL;

-- Email için index (arama performansı için)
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles (email);

-- Created_at için index (sıralama performansı için)
CREATE INDEX IF NOT EXISTS profiles_created_at_idx ON public.profiles (created_at);

-- Username validation constraint
ALTER TABLE public.profiles
ADD CONSTRAINT IF NOT EXISTS profiles_username_format
CHECK (username IS NULL OR (
  length(username) >= 3 AND
  length(username) <= 30 AND
  username ~ '^[a-z0-9_]+$'
));

-- Email validation constraint
ALTER TABLE public.profiles
ADD CONSTRAINT IF NOT EXISTS profiles_email_format
CHECK (email IS NULL OR email ~ '^[^@]+@[^@]+\.[^@]+$');

-- 11. MEVCUT KULLANICILAR İÇİN PROFİL OLUŞTUR (eğer yoksa)
INSERT INTO public.profiles (id, full_name, email, created_at, updated_at)
SELECT
  u.id,
  COALESCE(u.raw_user_meta_data->>'full_name', ''),
  u.email,
  COALESCE(u.created_at, now()),
  now()
FROM auth.users u
WHERE u.id NOT IN (SELECT id FROM public.profiles)
ON CONFLICT (id) DO NOTHING;

-- 12. PROFIL GÜNCELLEME FONKSIYONU
-- Profil güncellendiğinde updated_at otomatik güncelle
CREATE OR REPLACE FUNCTION public.update_profiles_updated_at()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Updated_at trigger'ı oluştur
DROP TRIGGER IF EXISTS profiles_updated_at_trigger ON public.profiles;
CREATE TRIGGER profiles_updated_at_trigger
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_profiles_updated_at();
