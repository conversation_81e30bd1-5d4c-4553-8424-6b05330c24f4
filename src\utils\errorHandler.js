// Comprehensive error handling utility for SipTracker
// Provides Turkish error messages and proper error categorization

export const ErrorTypes = {
  NETWORK: 'network',
  AUTH: 'auth',
  PROFILE: 'profile',
  VALIDATION: 'validation',
  PERMISSION: 'permission',
  DATABASE: 'database',
  UNKNOWN: 'unknown'
};

export const ErrorSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Categorize errors based on error codes and messages
export const categorizeError = (error) => {
  if (!error) return { type: ErrorTypes.UNKNOWN, severity: ErrorSeverity.LOW };

  const errorCode = error.code;
  const errorMessage = error.message?.toLowerCase() || '';

  // Network errors
  if (errorMessage.includes('network') || 
      errorMessage.includes('fetch') || 
      errorMessage.includes('timeout') ||
      errorMessage.includes('connection')) {
    return { type: ErrorTypes.NETWORK, severity: ErrorSeverity.MEDIUM };
  }

  // Authentication errors
  if (errorCode === 'invalid_credentials' || 
      errorCode === 'email_not_confirmed' ||
      errorCode === 'weak_password' ||
      errorCode === 'signup_disabled' ||
      errorMessage.includes('auth')) {
    return { type: ErrorTypes.AUTH, severity: ErrorSeverity.HIGH };
  }

  // Permission errors
  if (errorCode === '42501' || 
      errorCode === 'insufficient_privilege' ||
      errorMessage.includes('permission') ||
      errorMessage.includes('access denied')) {
    return { type: ErrorTypes.PERMISSION, severity: ErrorSeverity.HIGH };
  }

  // Profile-specific errors
  if (errorCode === 'PGRST116' || // Profile not found
      errorCode === 'PGRST301' || // Profile creation failed
      errorCode === '23505') {     // Unique constraint violation (username taken)
    return { type: ErrorTypes.PROFILE, severity: ErrorSeverity.MEDIUM };
  }

  // Validation errors
  if (errorCode === '23514' || // Check constraint violation
      errorMessage.includes('validation') ||
      errorMessage.includes('invalid')) {
    return { type: ErrorTypes.VALIDATION, severity: ErrorSeverity.LOW };
  }

  // Database errors
  if (errorCode?.startsWith('23') || // PostgreSQL constraint violations
      errorCode?.startsWith('42') || // PostgreSQL syntax/access errors
      errorMessage.includes('database') ||
      errorMessage.includes('sql')) {
    return { type: ErrorTypes.DATABASE, severity: ErrorSeverity.HIGH };
  }

  return { type: ErrorTypes.UNKNOWN, severity: ErrorSeverity.MEDIUM };
};

// Get Turkish error messages based on error type and context
export const getErrorMessage = (error, context = 'general') => {
  const { type, severity } = categorizeError(error);
  const errorCode = error?.code;

  // Context-specific error messages
  const contextMessages = {
    login: {
      [ErrorTypes.NETWORK]: 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.',
      [ErrorTypes.AUTH]: getAuthErrorMessage(error),
      [ErrorTypes.PERMISSION]: 'Giriş yetkiniz bulunmuyor. Lütfen yöneticinizle iletişime geçin.',
      [ErrorTypes.DATABASE]: 'Sunucu ile bağlantı kurulamadı. Lütfen daha sonra tekrar deneyin.',
      [ErrorTypes.UNKNOWN]: 'Giriş yaparken bir hata oluştu. Lütfen tekrar deneyin.'
    },
    register: {
      [ErrorTypes.NETWORK]: 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.',
      [ErrorTypes.AUTH]: getRegisterErrorMessage(error),
      [ErrorTypes.VALIDATION]: 'Girdiğiniz bilgileri kontrol edin ve tekrar deneyin.',
      [ErrorTypes.DATABASE]: 'Kayıt işlemi şu anda gerçekleştirilemiyor. Lütfen daha sonra tekrar deneyin.',
      [ErrorTypes.UNKNOWN]: 'Kayıt olurken bir hata oluştu. Lütfen tekrar deneyin.'
    },
    profile: {
      [ErrorTypes.NETWORK]: 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.',
      [ErrorTypes.PROFILE]: getProfileErrorMessage(error),
      [ErrorTypes.PERMISSION]: 'Profil erişim yetkiniz bulunmuyor. Lütfen çıkış yapıp tekrar giriş yapın.',
      [ErrorTypes.DATABASE]: 'Profil bilgileri yüklenemedi. Lütfen daha sonra tekrar deneyin.',
      [ErrorTypes.UNKNOWN]: 'Profil işlemi sırasında bir hata oluştu.'
    },
    onboarding: {
      [ErrorTypes.NETWORK]: 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.',
      [ErrorTypes.PROFILE]: getOnboardingErrorMessage(error),
      [ErrorTypes.VALIDATION]: 'Girdiğiniz bilgileri kontrol edin ve tekrar deneyin.',
      [ErrorTypes.PERMISSION]: 'Profil oluşturma yetkiniz bulunmuyor. Lütfen çıkış yapıp tekrar giriş yapın.',
      [ErrorTypes.DATABASE]: 'Profil oluşturulamadı. Lütfen daha sonra tekrar deneyin.',
      [ErrorTypes.UNKNOWN]: 'Profil oluşturulurken bir hata oluştu.'
    }
  };

  const messages = contextMessages[context] || contextMessages.general || {};
  return messages[type] || 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.';
};

// Specific error message functions
const getAuthErrorMessage = (error) => {
  switch (error?.code) {
    case 'invalid_credentials':
      return 'E-posta veya şifre hatalı. Lütfen kontrol edin.';
    case 'email_not_confirmed':
      return 'E-posta adresinizi doğrulamanız gerekiyor. E-postanızı kontrol edin.';
    case 'too_many_requests':
      return 'Çok fazla deneme yaptınız. Lütfen bir süre bekleyin.';
    case 'weak_password':
      return 'Şifreniz çok zayıf. Daha güçlü bir şifre seçin.';
    default:
      return 'Giriş yaparken bir hata oluştu. Lütfen bilgilerinizi kontrol edin.';
  }
};

const getRegisterErrorMessage = (error) => {
  switch (error?.code) {
    case 'email_address_invalid':
      return 'Geçersiz e-posta adresi. Lütfen doğru bir e-posta girin.';
    case 'weak_password':
      return 'Şifreniz çok zayıf. En az 6 karakter olmalı.';
    case 'signup_disabled':
      return 'Yeni kayıtlar şu anda kapalı. Lütfen daha sonra tekrar deneyin.';
    case 'email_address_not_authorized':
      return 'Bu e-posta adresi ile kayıt olma yetkiniz bulunmuyor.';
    default:
      return 'Kayıt olurken bir hata oluştu. Lütfen bilgilerinizi kontrol edin.';
  }
};

const getProfileErrorMessage = (error) => {
  switch (error?.code) {
    case 'PGRST116':
      return 'Profil bulunamadı. Onboarding işlemi başlatılıyor...';
    case 'PGRST301':
      return 'Profil oluşturulamadı. Lütfen tekrar giriş yapın.';
    case '42501':
      return 'Profil erişim yetkisi yok. Lütfen çıkış yapıp tekrar giriş yapın.';
    default:
      return 'Profil işlemi sırasında bir hata oluştu.';
  }
};

const getOnboardingErrorMessage = (error) => {
  switch (error?.code) {
    case '23505':
      return 'Bu kullanıcı adı zaten alınmış. Lütfen başka bir kullanıcı adı deneyin.';
    case 'PGRST301':
      return 'Profil oluşturulamadı. Lütfen tekrar giriş yapın.';
    case 'PGRST116':
      return 'Profil bulunamadı. Profil oluşturma işlemi başlatılıyor...';
    case '42501':
      return 'Yetki hatası. Lütfen çıkış yapıp tekrar giriş yapın.';
    default:
      return `Profil güncellenirken hata oluştu: ${error?.message || 'Bilinmeyen hata'}`;
  }
};

// Get user-friendly error title based on severity
export const getErrorTitle = (error, context = 'general') => {
  const { severity } = categorizeError(error);
  
  const titles = {
    [ErrorSeverity.LOW]: 'Uyarı',
    [ErrorSeverity.MEDIUM]: 'Hata',
    [ErrorSeverity.HIGH]: 'Önemli Hata',
    [ErrorSeverity.CRITICAL]: 'Kritik Hata'
  };

  return titles[severity] || 'Hata';
};

// Check if error is retryable
export const isRetryableError = (error) => {
  const { type } = categorizeError(error);
  return [ErrorTypes.NETWORK, ErrorTypes.DATABASE].includes(type);
};

// Get retry delay based on error type (in milliseconds)
export const getRetryDelay = (error, attemptNumber = 1) => {
  const { type } = categorizeError(error);
  
  const baseDelays = {
    [ErrorTypes.NETWORK]: 1000,
    [ErrorTypes.DATABASE]: 2000,
    [ErrorTypes.UNKNOWN]: 1500
  };

  const baseDelay = baseDelays[type] || 1000;
  
  // Exponential backoff with jitter
  return baseDelay * Math.pow(2, attemptNumber - 1) + Math.random() * 500;
};

// Log error with context for debugging
export const logError = (error, context, additionalInfo = {}) => {
  const { type, severity } = categorizeError(error);
  
  console.error(`[${context.toUpperCase()}] ${severity.toUpperCase()} ${type.toUpperCase()} ERROR:`, {
    error: error,
    code: error?.code,
    message: error?.message,
    context: context,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  });
};

export default {
  ErrorTypes,
  ErrorSeverity,
  categorizeError,
  getErrorMessage,
  getErrorTitle,
  isRetryableError,
  getRetryDelay,
  logError
};
