-- SipTracker Onboarding Flow Database Fixes
-- Bu komutları Supabase SQL Editor'de sırayla çalıştırın
-- Mevcut kullanıcı verilerini koruyarak onboarding akışını düzeltir

-- ============================================================================
-- 1. PROFILES TABLOSU YAPISI KONTROLÜ VE DÜZELTMESİ
-- ============================================================================

-- Profiles tablosunun mevcut olduğundan emin ol
CREATE TABLE IF NOT EXISTS public.profiles (
  id uuid REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name text,
  username text,
  email text,
  coffee_preferences jsonb DEFAULT '[]'::jsonb,
  usage_purpose jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- ============================================================================
-- 2. CONSTRAINT'LER VE İNDEXLER
-- ============================================================================

-- Username için unique constraint (null değerler hariç)
DROP INDEX IF EXISTS profiles_username_unique;
CREATE UNIQUE INDEX profiles_username_unique 
ON public.profiles (username) 
WHERE username IS NOT NULL;

-- Email için index (arama performansı için)
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles (email);

-- Created_at için index (sıralama performansı için)
CREATE INDEX IF NOT EXISTS profiles_created_at_idx ON public.profiles (created_at);

-- Username validation constraint
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_username_format;
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_username_format 
CHECK (username IS NULL OR (
  length(username) >= 3 AND 
  length(username) <= 30 AND 
  username ~ '^[a-z0-9_]+$'
));

-- Email validation constraint
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_email_format;
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_email_format 
CHECK (email IS NULL OR email ~ '^[^@]+@[^@]+\.[^@]+$');

-- ============================================================================
-- 3. GELIŞMIŞ OTOMATIK PROFIL OLUŞTURMA TRIGGER'I
-- ============================================================================

-- Kullanıcı kaydolduğunda otomatik profil oluştur
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Profil zaten varsa işlem yapma (duplicate key hatası önleme)
  IF EXISTS (SELECT 1 FROM public.profiles WHERE id = new.id) THEN
    RAISE LOG 'Profile already exists for user: %', new.id;
    RETURN new;
  END IF;
  
  -- Yeni profil oluştur
  INSERT INTO public.profiles (
    id, 
    full_name, 
    email,
    username,
    coffee_preferences,
    usage_purpose,
    created_at,
    updated_at
  )
  VALUES (
    new.id, 
    COALESCE(new.raw_user_meta_data->>'full_name', ''), 
    new.email,
    NULL, -- Username onboarding sırasında set edilecek
    '[]'::jsonb, -- Boş array, onboarding sırasında doldurulacak
    '[]'::jsonb, -- Boş array, onboarding sırasında doldurulacak
    now(),
    now()
  );
  
  -- Log oluştur
  RAISE LOG 'Profile created for user: % with email: %', new.id, new.email;
  
  RETURN new;
EXCEPTION
  WHEN unique_violation THEN
    -- Profil zaten varsa sessizce devam et
    RAISE LOG 'Profile already exists for user: %', new.id;
    RETURN new;
  WHEN OTHERS THEN
    -- Diğer hatalar için log tut ama işlemi durdurma
    RAISE LOG 'Error creating profile for user %: %', new.id, SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Eski trigger'ı sil ve yenisini oluştur
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- 4. PROFIL GÜNCELLEME TRIGGER'I
-- ============================================================================

-- Profil güncellendiğinde updated_at otomatik güncelle
CREATE OR REPLACE FUNCTION public.update_profiles_updated_at()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Updated_at trigger'ı oluştur
DROP TRIGGER IF EXISTS profiles_updated_at_trigger ON public.profiles;
CREATE TRIGGER profiles_updated_at_trigger
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_profiles_updated_at();

-- ============================================================================
-- 5. ROW LEVEL SECURITY (RLS) POLİTİKALARI
-- ============================================================================

-- RLS'yi aktif et
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Mevcut politikaları sil
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;

-- Yeni politikalar oluştur
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- ============================================================================
-- 6. MEVCUT KULLANICILAR İÇİN PROFİL OLUŞTUR
-- ============================================================================

-- Mevcut kullanıcılar için eksik profilleri oluştur
INSERT INTO public.profiles (id, full_name, email, created_at, updated_at)
SELECT 
  u.id, 
  COALESCE(u.raw_user_meta_data->>'full_name', ''), 
  u.email,
  COALESCE(u.created_at, now()),
  now()
FROM auth.users u
WHERE u.id NOT IN (SELECT id FROM public.profiles)
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 7. VERİFİKASYON VE TEST
-- ============================================================================

-- Profil sayısını kontrol et
DO $$
DECLARE
  user_count integer;
  profile_count integer;
BEGIN
  SELECT COUNT(*) INTO user_count FROM auth.users;
  SELECT COUNT(*) INTO profile_count FROM public.profiles;
  
  RAISE NOTICE 'Total users: %, Total profiles: %', user_count, profile_count;
  
  IF user_count != profile_count THEN
    RAISE WARNING 'User count (%) does not match profile count (%). Some profiles may be missing.', user_count, profile_count;
  ELSE
    RAISE NOTICE 'All users have profiles. Database is consistent.';
  END IF;
END $$;

-- Eksik profilleri listele (varsa)
SELECT 
  u.id as user_id,
  u.email,
  u.created_at as user_created_at
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- ============================================================================
-- 8. BAŞARILI TAMAMLAMA MESAJI
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '✅ SipTracker onboarding database fixes completed successfully!';
  RAISE NOTICE '📋 Summary:';
  RAISE NOTICE '   - Enhanced profile creation trigger installed';
  RAISE NOTICE '   - Username uniqueness and validation constraints added';
  RAISE NOTICE '   - Automatic updated_at timestamp trigger added';
  RAISE NOTICE '   - Row Level Security policies updated';
  RAISE NOTICE '   - Missing profiles created for existing users';
  RAISE NOTICE '🚀 Your onboarding flow should now work properly!';
END $$;
